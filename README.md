# 运营表格数据自动拆分程序

这是一个基于PyQt6、xlwings和PIL开发的自动拆分运营表格数据的程序。

## 功能特点

1. **多文件支持**: 可以读取多个运营表格和多sheet表格
2. **按sheet处理**: 每个sheet表格单独处理，不汇总在一起
3. **智能拆分**: 将搜索关键词和单数汇总，根据客服数量进行拆分
4. **精确修改**: 只改变C2-C10和D2-D10的内容，其他地方不做改变
5. **图片复制**: 每个sheet表格内的图片会被复制到拆分后的文件中
6. **整体拆分**: 以sheet为单位进行拆分，保持数据完整性

## 数据列说明

程序处理的表格包含以下数据列：
- **搜索主图** (A列)
- **商品ID** (B列)  
- **搜索关键词** (C列) - 会被修改
- **单数** (D列) - 会被修改
- **合计单量** (E列)
- **需求备注** (F列)

## 安装要求

### 系统要求
- Windows 10/11
- Python 3.8+
- Microsoft Excel (用于xlwings)

### Python依赖
```bash
pip install -r requirements.txt
```

主要依赖包：
- PyQt6 (GUI界面)
- xlwings (Excel文件处理)
- Pillow (图片处理)
- openpyxl (Excel文件支持)

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 添加Excel文件
- 点击"添加文件"按钮选择Excel文件
- 或者直接拖拽Excel文件到文件列表区域
- 支持.xlsx和.xls格式

### 3. 设置拆分参数
- **客服数量**: 设置要拆分给多少个客服（1-10个）
- **输出文件夹**: 选择拆分结果的保存位置

### 4. 开始拆分
- 点击"开始拆分"按钮
- 程序会显示处理进度和日志信息
- 完成后会在输出文件夹生成拆分结果

## 拆分逻辑

1. **数据分析**: 程序首先分析所有工作表中C2-C10和D2-D10的数据
2. **关键词汇总**: 收集所有搜索关键词和对应的单数
3. **按sheet分配**: 将整个sheet分配给不同的客服，而不是按行拆分
4. **数量重新分配**: 在每个客服的sheet中，重新计算C2-C10和D2-D10的数量分配
5. **图片复制**: 保持原有图片在对应位置

## 输出文件

拆分后的文件命名格式：
```
原文件名_客服1_3份.xlsx
原文件名_客服2_3份.xlsx
原文件名_客服3_3份.xlsx
```

## 文件结构

```
SEO拆分/
├── main.py                 # 主程序入口
├── gui_components.py       # GUI界面组件
├── excel_processor.py      # Excel文件处理
├── data_splitter.py        # 数据拆分逻辑
├── config.py              # 配置文件
├── utils.py               # 工具函数
├── requirements.txt       # 依赖包列表
├── README.md             # 说明文档
├── output/               # 输出文件夹
├── backup/               # 备份文件夹
├── temp/                 # 临时文件夹
└── logs/                 # 日志文件夹
```

## 注意事项

1. **Excel要求**: 确保安装了Microsoft Excel，xlwings需要Excel环境
2. **文件格式**: 支持.xlsx和.xls格式的Excel文件
3. **数据范围**: 程序只处理C2-C10和D2-D10范围内的数据
4. **备份**: 程序会自动创建原文件的备份
5. **图片**: 图片复制功能依赖于Excel的图片格式支持

## 故障排除

### 常见问题

1. **Excel启动失败**
   - 确保安装了Microsoft Excel
   - 检查Excel是否被其他程序占用

2. **图片复制失败**
   - 检查图片格式是否支持
   - 确保图片文件没有损坏

3. **文件保存失败**
   - 检查输出文件夹权限
   - 确保磁盘空间充足

### 日志查看
程序运行日志保存在 `logs/splitter.log` 文件中，可以查看详细的错误信息。

## 技术支持

如有问题或建议，请查看日志文件或联系开发团队。
