#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试脚本
"""

import sys
import os

def test_imports():
    """测试所有模块的导入"""
    print("测试模块导入...")
    
    try:
        import config
        print("✓ config 模块导入成功")
    except Exception as e:
        print(f"✗ config 模块导入失败: {e}")
        return False
    
    try:
        import utils
        print("✓ utils 模块导入成功")
    except Exception as e:
        print(f"✗ utils 模块导入失败: {e}")
        return False
    
    try:
        import data_splitter
        print("✓ data_splitter 模块导入成功")
    except Exception as e:
        print(f"✗ data_splitter 模块导入失败: {e}")
        return False
    
    try:
        import excel_processor
        print("✓ excel_processor 模块导入成功")
    except Exception as e:
        print(f"✗ excel_processor 模块导入失败: {e}")
        return False
    
    try:
        import gui_components
        print("✓ gui_components 模块导入成功")
    except Exception as e:
        print(f"✗ gui_components 模块导入失败: {e}")
        return False
    
    return True

def test_dependencies():
    """测试依赖包"""
    print("\n测试依赖包...")
    
    dependencies = [
        ('PyQt6', 'PyQt6'),
        ('xlwings', 'xlwings'),
        ('PIL', 'PIL'),
        ('openpyxl', 'openpyxl'),
    ]
    
    all_ok = True
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✓ {name} 可用")
        except ImportError as e:
            print(f"✗ {name} 不可用: {e}")
            all_ok = False
    
    return all_ok

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from config import ensure_folders, APP_CONFIG, EXCEL_CONFIG
        
        # 测试文件夹创建
        ensure_folders()
        print("✓ 文件夹创建成功")
        
        # 测试配置访问
        print(f"✓ 应用名称: {APP_CONFIG['app_name']}")
        print(f"✓ 支持的Excel格式: {EXCEL_CONFIG['supported_formats']}")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_utils():
    """测试工具函数"""
    print("\n测试工具函数...")
    
    try:
        from utils import calculate_split_distribution, validate_customer_service_count
        
        # 测试拆分分配计算
        distribution = calculate_split_distribution(10, 3)
        print(f"✓ 拆分分配计算: 10个项目分给3个客服 = {distribution}")
        
        # 测试客服数量验证
        valid = validate_customer_service_count(3)
        print(f"✓ 客服数量验证: 3个客服 = {valid}")
        
        return True
    except Exception as e:
        print(f"✗ 工具函数测试失败: {e}")
        return False

def test_data_splitter():
    """测试数据拆分器"""
    print("\n测试数据拆分器...")
    
    try:
        from data_splitter import DataSplitter
        
        splitter = DataSplitter()
        print("✓ 数据拆分器创建成功")
        
        # 创建测试数据
        test_sheet_data = {
            'name': 'Sheet1',
            'data': {
                'C2': '关键词1',
                'D2': 5,
                'C3': '关键词2', 
                'D3': 3,
                'C4': '关键词3',
                'D4': 2
            },
            'images': []
        }
        
        analysis = splitter.analyze_sheet_data(test_sheet_data)
        print(f"✓ 工作表分析: 关键词数量={len(analysis['keywords'])}, 总数量={analysis['total_quantity']}")
        
        return True
    except Exception as e:
        print(f"✗ 数据拆分器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("运营表格数据自动拆分程序 - 基本功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_dependencies,
        test_config,
        test_utils,
        test_data_splitter,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有基本功能测试通过！")
        return True
    else:
        print("✗ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
