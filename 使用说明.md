# 运营表格数据自动拆分程序 - 使用说明

## 快速开始

### 1. 启动程序
双击 `run.bat` 文件或在命令行中运行：
```bash
python main.py
```

### 2. 程序界面说明

程序界面分为左右两个面板：

**左侧面板（操作区域）：**
- **Excel文件选择区域**：显示要处理的Excel文件列表
- **拆分设置区域**：设置客服数量和输出文件夹
- **开始拆分按钮**：启动拆分处理

**右侧面板（信息显示）：**
- **处理进度**：显示当前处理进度和状态
- **处理日志**：显示详细的处理日志信息
- **文件信息预览**：显示选中文件的详细信息

### 3. 操作步骤

#### 步骤1：添加Excel文件
- **方法1**：点击"添加文件"按钮，选择Excel文件
- **方法2**：直接拖拽Excel文件到文件列表区域
- 支持同时添加多个文件
- 支持.xlsx和.xls格式

#### 步骤2：设置拆分参数
- **客服数量**：设置要拆分给多少个客服（1-10个）
- **输出文件夹**：选择拆分结果的保存位置（默认为./output）

#### 步骤3：开始拆分
- 点击"开始拆分"按钮
- 观察右侧的进度显示和日志信息
- 等待处理完成

## 拆分规则详解

### 数据处理范围
- **只修改C2-C10和D2-D10的内容**
- 其他单元格内容保持不变
- 图片会被复制到拆分后的文件中

### 拆分逻辑
1. **按sheet为单位拆分**：不会将单个sheet的内容拆分，而是将整个sheet分配给不同客服
2. **数量重新分配**：在分配给客服的sheet中，重新计算C2-C10和D2-D10的数量分配
3. **关键词保持**：搜索关键词（C列）保持不变
4. **图片复制**：每个sheet中的图片会被复制到对应的拆分文件中

### 分配策略
- 如果有3个客服，3个sheet，则每个客服分配1个sheet
- 如果有3个客服，4个sheet，则分配为：客服1得到2个sheet，客服2和客服3各得到1个sheet
- 数量分配尽量平均，余数分配给前面的客服

## 输出文件说明

### 文件命名
拆分后的文件命名格式：
```
原文件名_客服1_3份.xlsx
原文件名_客服2_3份.xlsx
原文件名_客服3_3份.xlsx
```

### 文件内容
- 每个输出文件包含分配给对应客服的完整sheet
- C2-C10和D2-D10的数量会根据分配重新计算
- 其他数据保持原样
- 图片位置和大小保持不变

## 示例文件

程序提供了两个示例Excel文件用于测试：
- `示例运营表格.xlsx`：包含3个sheet（产品组1、产品组2、产品组3）
- `示例运营表格2.xlsx`：包含1个sheet（电子产品）

可以使用这些文件测试拆分功能。

## 常见问题

### Q1：程序启动失败
**A1：** 检查以下项目：
- 确保安装了Python 3.8+
- 确保安装了Microsoft Excel
- 运行 `pip install -r requirements.txt` 安装依赖

### Q2：Excel文件处理失败
**A2：** 可能的原因：
- Excel文件被其他程序占用，请关闭Excel
- 文件格式不支持，请使用.xlsx或.xls格式
- 文件损坏，请检查文件完整性

### Q3：图片复制失败
**A3：** 这是正常现象，程序会：
- 尝试复制原始图片
- 如果失败，会创建占位符图片
- 不影响数据拆分功能

### Q4：拆分结果不符合预期
**A4：** 请检查：
- 原始数据是否在C2-C10和D2-D10范围内
- 数量列（D列）是否为数字格式
- 查看日志文件了解详细信息

## 技术支持

### 日志文件
程序运行日志保存在 `logs/splitter.log`，包含详细的处理信息和错误信息。

### 备份文件
程序会自动在 `backup` 文件夹中创建原始文件的备份。

### 临时文件
处理过程中的临时文件保存在 `temp` 文件夹中，程序结束后会自动清理。

## 注意事项

1. **Excel环境**：程序依赖Microsoft Excel，请确保已安装
2. **文件权限**：确保对输出文件夹有写入权限
3. **磁盘空间**：确保有足够的磁盘空间保存拆分结果
4. **数据备份**：建议在处理重要数据前先备份
5. **单次处理**：建议一次处理的文件数量不要过多，以免影响性能

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多Excel文件和多sheet处理
- 实现按sheet为单位的拆分逻辑
- 支持图片复制功能
- 提供友好的GUI界面
