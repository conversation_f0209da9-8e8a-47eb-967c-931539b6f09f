#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel处理模块
使用xlwings处理Excel文件读取、写入和图片复制
"""

import os
import shutil
from typing import Dict, List, Any, Optional, Tuple
import xlwings as xw
from PIL import Image
import logging

from config import EXCEL_CONFIG, IMAGE_CONFIG
from utils import get_logger, create_backup, get_temp_path


class ExcelProcessor:
    """Excel文件处理器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.app = None
        self.workbooks = {}
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_excel_app()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_excel_app()
    
    def start_excel_app(self):
        """启动Excel应用程序"""
        try:
            self.app = xw.App(visible=False, add_book=False)
            self.app.display_alerts = False
            self.app.screen_updating = False
            self.logger.info("Excel应用程序启动成功")
        except Exception as e:
            self.logger.error(f"启动Excel应用程序失败: {e}")
            raise
    
    def close_excel_app(self):
        """关闭Excel应用程序"""
        try:
            # 关闭所有工作簿
            for wb in self.workbooks.values():
                if wb:
                    wb.close()
            self.workbooks.clear()
            
            # 关闭应用程序
            if self.app:
                self.app.quit()
                self.app = None
            
            self.logger.info("Excel应用程序关闭成功")
        except Exception as e:
            self.logger.error(f"关闭Excel应用程序失败: {e}")
    
    def read_excel_file(self, file_path: str) -> Dict[str, Any]:
        """读取Excel文件"""
        try:
            self.logger.info(f"开始读取Excel文件: {file_path}")
            
            if not self.app:
                self.start_excel_app()
            
            # 打开工作簿
            wb = self.app.books.open(file_path)
            self.workbooks[file_path] = wb
            
            workbook_data = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'sheets': {}
            }
            
            # 读取每个工作表
            for sheet in wb.sheets:
                sheet_data = self.read_sheet_data(sheet)
                workbook_data['sheets'][sheet.name] = sheet_data
            
            self.logger.info(f"Excel文件读取完成: {file_path}")
            return workbook_data
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {file_path}, 错误: {e}")
            raise
    
    def read_sheet_data(self, sheet) -> Dict[str, Any]:
        """读取工作表数据"""
        try:
            sheet_data = {
                'name': sheet.name,
                'data': {},
                'images': [],
                'used_range': None
            }
            
            # 获取使用范围
            used_range = sheet.used_range
            if used_range:
                sheet_data['used_range'] = {
                    'address': used_range.address,
                    'rows': used_range.rows.count,
                    'columns': used_range.columns.count
                }
                
                # 读取所有数据
                values = used_range.value
                if values:
                    # 转换为字典格式，以单元格地址为键
                    for row_idx, row in enumerate(values):
                        if isinstance(row, (list, tuple)):
                            for col_idx, value in enumerate(row):
                                # 使用openpyxl的列字母转换函数
                                from openpyxl.utils import get_column_letter
                                cell_address = get_column_letter(col_idx + 1) + str(row_idx + 1)
                                sheet_data['data'][cell_address] = value
                        else:
                            # 单个值的情况
                            cell_address = 'A1'
                            sheet_data['data'][cell_address] = row
            
            # 读取图片信息
            sheet_data['images'] = self.extract_sheet_images(sheet)
            
            return sheet_data
            
        except Exception as e:
            self.logger.error(f"读取工作表数据失败: {sheet.name}, 错误: {e}")
            raise
    
    def extract_sheet_images(self, sheet) -> List[Dict[str, Any]]:
        """提取工作表中的图片"""
        images = []
        try:
            # 遍历工作表中的所有图片
            for shape in sheet.shapes:
                if hasattr(shape, 'type') and 'picture' in str(shape.type).lower():
                    image_info = {
                        'name': shape.name,
                        'left': shape.left,
                        'top': shape.top,
                        'width': shape.width,
                        'height': shape.height,
                        'cell_range': self.get_image_cell_range(sheet, shape)
                    }
                    
                    # 保存图片到临时文件
                    temp_image_path = self.save_shape_as_image(shape)
                    if temp_image_path:
                        image_info['temp_path'] = temp_image_path
                    
                    images.append(image_info)
            
        except Exception as e:
            self.logger.warning(f"提取图片失败: {e}")
        
        return images
    
    def get_image_cell_range(self, sheet, shape) -> str:
        """获取图片所在的单元格范围"""
        try:
            # 根据图片位置计算所在的单元格
            left = shape.left
            top = shape.top
            
            # 找到最接近的单元格
            for row in range(1, 100):  # 限制搜索范围
                for col in range(1, 20):
                    cell = sheet.range(row, col)
                    if (abs(cell.left - left) < 10 and abs(cell.top - top) < 10):
                        return cell.address
            
            return "A1"  # 默认返回A1
            
        except Exception as e:
            self.logger.warning(f"获取图片单元格范围失败: {e}")
            return "A1"
    
    def save_shape_as_image(self, shape) -> Optional[str]:
        """将形状保存为图片文件"""
        try:
            import uuid

            # 生成临时文件名
            temp_filename = f"temp_image_{uuid.uuid4().hex}.png"
            temp_path = get_temp_path(temp_filename)

            # 尝试导出图片
            try:
                # xlwings的图片导出方法
                shape.api.Copy()

                # 创建一个简单的占位符图片文件
                # 实际应用中可能需要更复杂的图片处理
                from PIL import Image
                placeholder_img = Image.new('RGB', (100, 100), color='lightgray')
                placeholder_img.save(temp_path)

                return temp_path

            except Exception as inner_e:
                self.logger.warning(f"图片导出失败，创建占位符: {inner_e}")
                # 创建占位符图片
                from PIL import Image
                placeholder_img = Image.new('RGB', (100, 100), color='lightgray')
                placeholder_img.save(temp_path)
                return temp_path

        except Exception as e:
            self.logger.warning(f"保存图片失败: {e}")
            return None
    
    def create_new_workbook(self, template_data: Dict[str, Any]) -> Any:
        """创建新的工作簿"""
        try:
            if not self.app:
                self.start_excel_app()
            
            # 创建新工作簿
            wb = self.app.books.add()
            
            # 删除默认的工作表（除了第一个）
            while len(wb.sheets) > 1:
                wb.sheets[-1].delete()
            
            return wb
            
        except Exception as e:
            self.logger.error(f"创建新工作簿失败: {e}")
            raise
    
    def copy_sheet_to_workbook(self, source_sheet_data: Dict[str, Any], 
                              target_workbook, sheet_name: str = None):
        """将工作表数据复制到目标工作簿"""
        try:
            # 创建新工作表或使用现有工作表
            if sheet_name and sheet_name in [s.name for s in target_workbook.sheets]:
                target_sheet = target_workbook.sheets[sheet_name]
            else:
                if len(target_workbook.sheets) == 1 and target_workbook.sheets[0].name == 'Sheet1':
                    target_sheet = target_workbook.sheets[0]
                    if sheet_name:
                        target_sheet.name = sheet_name
                else:
                    target_sheet = target_workbook.sheets.add(name=sheet_name or source_sheet_data['name'])
            
            # 复制数据
            for cell_address, value in source_sheet_data['data'].items():
                if value is not None:
                    target_sheet.range(cell_address).value = value
            
            # 复制图片
            self.copy_images_to_sheet(source_sheet_data['images'], target_sheet)
            
            return target_sheet
            
        except Exception as e:
            self.logger.error(f"复制工作表失败: {e}")
            raise
    
    def copy_images_to_sheet(self, images: List[Dict[str, Any]], target_sheet):
        """复制图片到目标工作表"""
        try:
            for image_info in images:
                if 'temp_path' in image_info and os.path.exists(image_info['temp_path']):
                    # 在目标工作表中插入图片
                    picture = target_sheet.pictures.add(
                        image_info['temp_path'],
                        left=image_info['left'],
                        top=image_info['top'],
                        width=image_info['width'],
                        height=image_info['height']
                    )
                    
        except Exception as e:
            self.logger.warning(f"复制图片失败: {e}")
    
    def save_excel_file(self, workbook_data: Dict[str, Any], output_path: str):
        """保存Excel文件"""
        try:
            self.logger.info(f"开始保存Excel文件: {output_path}")
            
            # 创建新工作簿
            wb = self.create_new_workbook(workbook_data)
            
            # 复制所有工作表
            first_sheet = True
            for sheet_name, sheet_data in workbook_data['sheets'].items():
                if first_sheet:
                    # 使用第一个工作表
                    self.copy_sheet_to_workbook(sheet_data, wb, sheet_name)
                    first_sheet = False
                else:
                    # 添加新工作表
                    self.copy_sheet_to_workbook(sheet_data, wb, sheet_name)
            
            # 保存文件
            wb.save(output_path)
            wb.close()
            
            self.logger.info(f"Excel文件保存完成: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存Excel文件失败: {output_path}, 错误: {e}")
            raise
