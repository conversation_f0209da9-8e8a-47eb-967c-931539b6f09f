#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含应用程序的所有配置信息
"""

import os

# 应用程序基本配置
APP_CONFIG = {
    'app_name': '运营表格数据自动拆分程序',
    'version': '1.0.0',
    'organization': 'SEO拆分工具',
    'window_title': '运营表格数据自动拆分程序',
    'window_width': 1000,
    'window_height': 700,
    'min_width': 800,
    'min_height': 600,
}

# Excel处理配置
EXCEL_CONFIG = {
    'supported_formats': ['.xlsx', '.xls'],
    'target_columns': {
        'search_keyword': 'C',  # 搜索关键词列
        'quantity': 'D',        # 单数列
    },
    'target_rows': {
        'start': 2,  # 开始行
        'end': 10,   # 结束行
    },
    'data_columns': {
        'main_image': 'A',      # 搜索主图
        'product_id': 'B',      # 商品ID
        'search_keyword': 'C',  # 搜索关键词
        'quantity': 'D',        # 单数
        'total_quantity': 'E',  # 合计单量
        'requirement_note': 'F', # 需求备注
    }
}

# 图片处理配置
IMAGE_CONFIG = {
    'supported_formats': ['.jpg', '.jpeg', '.png', '.bmp', '.gif'],
    'max_width': 800,
    'max_height': 600,
    'quality': 85,
}

# 拆分配置
SPLIT_CONFIG = {
    'default_customer_service_count': 3,
    'min_customer_service_count': 1,
    'max_customer_service_count': 10,
    'output_folder_prefix': '拆分结果_',
    'backup_folder': 'backup',
}

# 日志配置
LOG_CONFIG = {
    'log_level': 'INFO',
    'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'log_file': 'splitter.log',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# 文件路径配置
PATH_CONFIG = {
    'temp_folder': 'temp',
    'output_folder': 'output',
    'backup_folder': 'backup',
    'log_folder': 'logs',
}

# 确保必要的文件夹存在
def ensure_folders():
    """确保必要的文件夹存在"""
    for folder in PATH_CONFIG.values():
        if not os.path.exists(folder):
            os.makedirs(folder, exist_ok=True)

# 获取输出文件路径
def get_output_path(filename):
    """获取输出文件的完整路径"""
    ensure_folders()
    return os.path.join(PATH_CONFIG['output_folder'], filename)

# 获取备份文件路径
def get_backup_path(filename):
    """获取备份文件的完整路径"""
    ensure_folders()
    return os.path.join(PATH_CONFIG['backup_folder'], filename)

# 获取临时文件路径
def get_temp_path(filename):
    """获取临时文件的完整路径"""
    ensure_folders()
    return os.path.join(PATH_CONFIG['temp_folder'], filename)

# 获取日志文件路径
def get_log_path(filename):
    """获取日志文件的完整路径"""
    ensure_folders()
    return os.path.join(PATH_CONFIG['log_folder'], filename)
