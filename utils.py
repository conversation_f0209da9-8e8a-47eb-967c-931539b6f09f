#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
包含各种实用工具函数
"""

import os
import shutil
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from config import LOG_CONFIG, PATH_CONFIG, get_log_path


def setup_logging():
    """设置日志系统"""
    log_file = get_log_path(LOG_CONFIG['log_file'])
    
    # 创建日志格式器
    formatter = logging.Formatter(LOG_CONFIG['log_format'])
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(getattr(logging, LOG_CONFIG['log_level']))
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOG_CONFIG['log_level']))
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器"""
    return logging.getLogger(name)


def validate_excel_file(file_path: str) -> bool:
    """验证Excel文件是否有效"""
    if not os.path.exists(file_path):
        return False
    
    file_ext = os.path.splitext(file_path)[1].lower()
    from config import EXCEL_CONFIG
    return file_ext in EXCEL_CONFIG['supported_formats']


def get_safe_filename(filename: str) -> str:
    """获取安全的文件名（移除非法字符）"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename


def create_backup(file_path: str) -> str:
    """创建文件备份"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    filename = os.path.basename(file_path)
    name, ext = os.path.splitext(filename)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"{name}_backup_{timestamp}{ext}"
    
    from config import get_backup_path
    backup_path = get_backup_path(backup_filename)
    
    shutil.copy2(file_path, backup_path)
    return backup_path


def clean_temp_files():
    """清理临时文件"""
    temp_folder = PATH_CONFIG['temp_folder']
    if os.path.exists(temp_folder):
        for file in os.listdir(temp_folder):
            file_path = os.path.join(temp_folder, file)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                logging.warning(f"清理临时文件失败: {file_path}, 错误: {e}")


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件信息"""
    if not os.path.exists(file_path):
        return {}
    
    stat = os.stat(file_path)
    return {
        'name': os.path.basename(file_path),
        'path': file_path,
        'size': stat.st_size,
        'size_formatted': format_file_size(stat.st_size),
        'modified': datetime.fromtimestamp(stat.st_mtime),
        'created': datetime.fromtimestamp(stat.st_ctime),
    }


def validate_customer_service_count(count: int) -> bool:
    """验证客服数量是否有效"""
    from config import SPLIT_CONFIG
    return (SPLIT_CONFIG['min_customer_service_count'] <= count <= 
            SPLIT_CONFIG['max_customer_service_count'])


def generate_output_filename(original_filename: str, customer_service_index: int, 
                           total_count: int) -> str:
    """生成输出文件名"""
    name, ext = os.path.splitext(original_filename)
    safe_name = get_safe_filename(name)
    return f"{safe_name}_客服{customer_service_index + 1}_{total_count}份{ext}"


def ensure_unique_filename(file_path: str) -> str:
    """确保文件名唯一（如果存在则添加数字后缀）"""
    if not os.path.exists(file_path):
        return file_path
    
    directory = os.path.dirname(file_path)
    filename = os.path.basename(file_path)
    name, ext = os.path.splitext(filename)
    
    counter = 1
    while True:
        new_filename = f"{name}_{counter}{ext}"
        new_path = os.path.join(directory, new_filename)
        if not os.path.exists(new_path):
            return new_path
        counter += 1


def get_temp_path(filename: str) -> str:
    """获取临时文件的完整路径"""
    from config import get_temp_path as config_get_temp_path
    return config_get_temp_path(filename)


def calculate_split_distribution(total_quantity: int, customer_service_count: int) -> List[int]:
    """计算拆分分配（尽量平均分配）"""
    if customer_service_count <= 0:
        return []

    base_quantity = total_quantity // customer_service_count
    remainder = total_quantity % customer_service_count

    distribution = [base_quantity] * customer_service_count

    # 将余数分配给前几个客服
    for i in range(remainder):
        distribution[i] += 1

    return distribution
