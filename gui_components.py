#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面组件模块
包含所有PyQt6界面组件
"""

import os
from typing import List
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QGroupBox, QPushButton, QLabel, QLineEdit, QTextEdit,
                            QListWidget, QProgressBar, QSpinBox, QFileDialog,
                            QMessageBox, QSplitter, QFrame, QGridLayout)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import <PERSON>Font, QPixmap, QIcon

from config import APP_CONFIG, SPLIT_CONFIG
from utils import get_logger, validate_excel_file, get_file_info


class FileListWidget(QListWidget):
    """文件列表组件"""
    
    files_changed = pyqtSignal(list)
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DragDropMode.InternalMove)
        self.file_paths = []
        
    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()
    
    def dropEvent(self, event):
        files = [url.toLocalFile() for url in event.mimeData().urls()]
        valid_files = [f for f in files if validate_excel_file(f)]
        
        for file_path in valid_files:
            if file_path not in self.file_paths:
                self.file_paths.append(file_path)
                self.addItem(os.path.basename(file_path))
        
        self.files_changed.emit(self.file_paths)
    
    def remove_selected(self):
        """移除选中的文件"""
        current_row = self.currentRow()
        if current_row >= 0:
            self.file_paths.pop(current_row)
            self.takeItem(current_row)
            self.files_changed.emit(self.file_paths)
    
    def clear_all(self):
        """清空所有文件"""
        self.file_paths.clear()
        self.clear()
        self.files_changed.emit(self.file_paths)


class ProcessingThread(QThread):
    """处理线程"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, file_paths, customer_service_count, output_folder):
        super().__init__()
        self.file_paths = file_paths
        self.customer_service_count = customer_service_count
        self.output_folder = output_folder
        self.logger = get_logger(__name__)
    
    def run(self):
        """运行处理任务"""
        try:
            from excel_processor import ExcelProcessor
            from data_splitter import DataSplitter
            
            processor = ExcelProcessor()
            splitter = DataSplitter()
            
            total_files = len(self.file_paths)
            
            for i, file_path in enumerate(self.file_paths):
                self.status_updated.emit(f"正在处理文件: {os.path.basename(file_path)}")
                
                # 读取Excel文件
                workbook_data = processor.read_excel_file(file_path)
                
                # 拆分数据
                split_results = splitter.split_workbook_data(
                    workbook_data, self.customer_service_count
                )
                
                # 保存拆分结果
                for j, result in enumerate(split_results):
                    output_filename = f"{os.path.splitext(os.path.basename(file_path))[0]}_客服{j+1}.xlsx"
                    output_path = os.path.join(self.output_folder, output_filename)
                    processor.save_excel_file(result, output_path)
                
                # 更新进度
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
            
            self.finished_signal.emit(True, "处理完成！")
            
        except Exception as e:
            self.logger.error(f"处理过程中发生错误: {e}")
            self.finished_signal.emit(False, f"处理失败: {str(e)}")


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.processing_thread = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(APP_CONFIG['window_title'])
        self.setGeometry(100, 100, APP_CONFIG['window_width'], APP_CONFIG['window_height'])
        self.setMinimumSize(APP_CONFIG['min_width'], APP_CONFIG['min_height'])
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
    def create_left_panel(self):
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 文件选择组
        file_group = QGroupBox("Excel文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 文件列表
        self.file_list = FileListWidget()
        self.file_list.files_changed.connect(self.on_files_changed)
        file_layout.addWidget(self.file_list)
        
        # 文件操作按钮
        file_buttons_layout = QHBoxLayout()
        
        self.add_files_btn = QPushButton("添加文件")
        self.add_files_btn.clicked.connect(self.add_files)
        file_buttons_layout.addWidget(self.add_files_btn)
        
        self.remove_file_btn = QPushButton("移除选中")
        self.remove_file_btn.clicked.connect(self.remove_selected_file)
        self.remove_file_btn.setEnabled(False)
        file_buttons_layout.addWidget(self.remove_file_btn)
        
        self.clear_files_btn = QPushButton("清空列表")
        self.clear_files_btn.clicked.connect(self.clear_files)
        self.clear_files_btn.setEnabled(False)
        file_buttons_layout.addWidget(self.clear_files_btn)
        
        file_layout.addLayout(file_buttons_layout)
        layout.addWidget(file_group)
        
        # 拆分设置组
        settings_group = QGroupBox("拆分设置")
        settings_layout = QGridLayout(settings_group)
        
        settings_layout.addWidget(QLabel("客服数量:"), 0, 0)
        self.customer_service_spin = QSpinBox()
        self.customer_service_spin.setRange(
            SPLIT_CONFIG['min_customer_service_count'],
            SPLIT_CONFIG['max_customer_service_count']
        )
        self.customer_service_spin.setValue(SPLIT_CONFIG['default_customer_service_count'])
        settings_layout.addWidget(self.customer_service_spin, 0, 1)
        
        settings_layout.addWidget(QLabel("输出文件夹:"), 1, 0)
        self.output_folder_edit = QLineEdit()
        self.output_folder_edit.setText("./output")
        settings_layout.addWidget(self.output_folder_edit, 1, 1)
        
        self.browse_output_btn = QPushButton("浏览")
        self.browse_output_btn.clicked.connect(self.browse_output_folder)
        settings_layout.addWidget(self.browse_output_btn, 1, 2)
        
        layout.addWidget(settings_group)
        
        # 操作按钮
        self.start_btn = QPushButton("开始拆分")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setEnabled(False)
        layout.addWidget(self.start_btn)
        
        layout.addStretch()
        return panel

    def create_right_panel(self):
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # 进度显示组
        progress_group = QGroupBox("处理进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("等待开始...")
        progress_layout.addWidget(self.status_label)

        layout.addWidget(progress_group)

        # 日志显示组
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_group)

        # 结果预览组
        preview_group = QGroupBox("文件信息预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)

        layout.addWidget(preview_group)

        return panel

    def add_files(self):
        """添加文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择Excel文件", "", "Excel文件 (*.xlsx *.xls)"
        )

        for file_path in files:
            if validate_excel_file(file_path) and file_path not in self.file_list.file_paths:
                self.file_list.file_paths.append(file_path)
                self.file_list.addItem(os.path.basename(file_path))

        self.file_list.files_changed.emit(self.file_list.file_paths)

    def remove_selected_file(self):
        """移除选中的文件"""
        self.file_list.remove_selected()

    def clear_files(self):
        """清空文件列表"""
        self.file_list.clear_all()

    def browse_output_folder(self):
        """浏览输出文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.output_folder_edit.setText(folder)

    def on_files_changed(self, file_paths):
        """文件列表变化时的处理"""
        has_files = len(file_paths) > 0
        self.remove_file_btn.setEnabled(has_files)
        self.clear_files_btn.setEnabled(has_files)
        self.start_btn.setEnabled(has_files)

        # 更新文件信息预览
        self.update_file_preview(file_paths)

    def update_file_preview(self, file_paths):
        """更新文件信息预览"""
        if not file_paths:
            self.preview_text.clear()
            return

        preview_text = "选中的文件信息:\n\n"
        for i, file_path in enumerate(file_paths, 1):
            file_info = get_file_info(file_path)
            preview_text += f"{i}. {file_info.get('name', '未知')}\n"
            preview_text += f"   路径: {file_path}\n"
            preview_text += f"   大小: {file_info.get('size_formatted', '未知')}\n"
            preview_text += f"   修改时间: {file_info.get('modified', '未知')}\n\n"

        self.preview_text.setPlainText(preview_text)

    def start_processing(self):
        """开始处理"""
        if not self.file_list.file_paths:
            QMessageBox.warning(self, "警告", "请先选择要处理的Excel文件！")
            return

        output_folder = self.output_folder_edit.text().strip()
        if not output_folder:
            QMessageBox.warning(self, "警告", "请选择输出文件夹！")
            return

        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)

        # 禁用开始按钮
        self.start_btn.setEnabled(False)
        self.start_btn.setText("处理中...")

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 清空日志
        self.log_text.clear()

        # 创建并启动处理线程
        self.processing_thread = ProcessingThread(
            self.file_list.file_paths,
            self.customer_service_spin.value(),
            output_folder
        )

        self.processing_thread.progress_updated.connect(self.progress_bar.setValue)
        self.processing_thread.status_updated.connect(self.update_status)
        self.processing_thread.finished_signal.connect(self.on_processing_finished)

        self.processing_thread.start()

    def update_status(self, message):
        """更新状态"""
        self.status_label.setText(message)
        self.log_text.append(f"[{QTimer().currentTime().toString()}] {message}")
        self.statusBar().showMessage(message)

    def on_processing_finished(self, success, message):
        """处理完成"""
        self.start_btn.setEnabled(True)
        self.start_btn.setText("开始拆分")
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("处理完成！")
            QMessageBox.information(self, "成功", message)
        else:
            self.status_label.setText("处理失败！")
            QMessageBox.critical(self, "错误", message)

        self.statusBar().showMessage("就绪")
