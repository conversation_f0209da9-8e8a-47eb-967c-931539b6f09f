#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据拆分逻辑模块
实现按客服数量拆分数据的逻辑
"""

import copy
from typing import Dict, List, Any, Tuple
from collections import defaultdict

from config import EXCEL_CONFIG
from utils import get_logger, calculate_split_distribution


class DataSplitter:
    """数据拆分器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def split_workbook_data(self, workbook_data: Dict[str, Any], 
                           customer_service_count: int) -> List[Dict[str, Any]]:
        """拆分工作簿数据"""
        try:
            self.logger.info(f"开始拆分工作簿数据，客服数量: {customer_service_count}")
            
            # 分析所有工作表的数据
            sheet_analysis = self.analyze_workbook_sheets(workbook_data)
            
            # 计算拆分策略
            split_strategy = self.calculate_split_strategy(sheet_analysis, customer_service_count)
            
            # 执行拆分
            split_results = self.execute_split(workbook_data, split_strategy)
            
            self.logger.info(f"工作簿数据拆分完成，生成 {len(split_results)} 个结果")
            return split_results
            
        except Exception as e:
            self.logger.error(f"拆分工作簿数据失败: {e}")
            raise
    
    def analyze_workbook_sheets(self, workbook_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析工作簿中所有工作表的数据"""
        analysis = {
            'sheets': {},
            'total_keywords': set(),
            'total_quantity': 0,
            'sheet_quantities': {}
        }
        
        for sheet_name, sheet_data in workbook_data['sheets'].items():
            sheet_analysis = self.analyze_sheet_data(sheet_data)
            analysis['sheets'][sheet_name] = sheet_analysis
            
            # 汇总关键词
            analysis['total_keywords'].update(sheet_analysis['keywords'])
            
            # 汇总数量
            sheet_quantity = sheet_analysis['total_quantity']
            analysis['total_quantity'] += sheet_quantity
            analysis['sheet_quantities'][sheet_name] = sheet_quantity
        
        # 转换set为list以便序列化
        analysis['total_keywords'] = list(analysis['total_keywords'])
        
        return analysis
    
    def analyze_sheet_data(self, sheet_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析单个工作表的数据"""
        analysis = {
            'keywords': set(),
            'quantities': [],
            'total_quantity': 0,
            'data_rows': []
        }
        
        # 分析C2-C10和D2-D10的数据
        target_rows = range(
            EXCEL_CONFIG['target_rows']['start'],
            EXCEL_CONFIG['target_rows']['end'] + 1
        )
        
        for row in target_rows:
            keyword_cell = f"C{row}"
            quantity_cell = f"D{row}"
            
            keyword = sheet_data['data'].get(keyword_cell)
            quantity = sheet_data['data'].get(quantity_cell)
            
            if keyword and str(keyword).strip():
                analysis['keywords'].add(str(keyword).strip())
                
                # 处理数量
                try:
                    qty = int(quantity) if quantity else 0
                    analysis['quantities'].append(qty)
                    analysis['total_quantity'] += qty
                    
                    analysis['data_rows'].append({
                        'row': row,
                        'keyword': str(keyword).strip(),
                        'quantity': qty
                    })
                except (ValueError, TypeError):
                    self.logger.warning(f"无法解析数量值: {quantity} 在行 {row}")
        
        return analysis
    
    def calculate_split_strategy(self, sheet_analysis: Dict[str, Any], 
                               customer_service_count: int) -> Dict[str, Any]:
        """计算拆分策略"""
        strategy = {
            'customer_service_count': customer_service_count,
            'sheet_assignments': {},
            'total_sheets': len(sheet_analysis['sheets'])
        }
        
        # 按sheet为单位进行拆分分配
        sheet_names = list(sheet_analysis['sheets'].keys())
        
        # 计算每个客服分配的sheet数量
        sheets_per_service = calculate_split_distribution(
            len(sheet_names), customer_service_count
        )
        
        # 分配sheet给客服
        sheet_index = 0
        for service_index, sheet_count in enumerate(sheets_per_service):
            assigned_sheets = []
            for _ in range(sheet_count):
                if sheet_index < len(sheet_names):
                    assigned_sheets.append(sheet_names[sheet_index])
                    sheet_index += 1
            
            strategy['sheet_assignments'][service_index] = assigned_sheets
        
        self.logger.info(f"拆分策略: {strategy}")
        return strategy
    
    def execute_split(self, workbook_data: Dict[str, Any], 
                     split_strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行拆分"""
        results = []
        
        for service_index in range(split_strategy['customer_service_count']):
            # 创建新的工作簿数据
            new_workbook_data = {
                'file_path': workbook_data['file_path'],
                'file_name': workbook_data['file_name'],
                'sheets': {}
            }
            
            # 获取分配给当前客服的sheet
            assigned_sheets = split_strategy['sheet_assignments'].get(service_index, [])
            
            for sheet_name in assigned_sheets:
                if sheet_name in workbook_data['sheets']:
                    # 复制整个sheet
                    original_sheet = workbook_data['sheets'][sheet_name]
                    new_sheet = copy.deepcopy(original_sheet)
                    
                    # 修改C2-C10和D2-D10的内容
                    new_sheet = self.modify_sheet_data(new_sheet, service_index)
                    
                    new_workbook_data['sheets'][sheet_name] = new_sheet
            
            if new_workbook_data['sheets']:  # 只有当有sheet时才添加结果
                results.append(new_workbook_data)
        
        return results
    
    def modify_sheet_data(self, sheet_data: Dict[str, Any], 
                         service_index: int) -> Dict[str, Any]:
        """修改工作表数据（只修改C2-C10和D2-D10）"""
        modified_sheet = copy.deepcopy(sheet_data)
        
        # 分析原始数据
        analysis = self.analyze_sheet_data(sheet_data)
        
        if not analysis['data_rows']:
            return modified_sheet
        
        # 计算新的数量分配
        total_quantity = analysis['total_quantity']
        if total_quantity == 0:
            return modified_sheet
        
        # 为当前客服分配数量（这里使用简单的平均分配策略）
        # 实际应用中可能需要更复杂的分配逻辑
        service_quantity = total_quantity // 3  # 假设3个客服
        if service_index == 0:  # 第一个客服分配余数
            service_quantity += total_quantity % 3
        
        # 重新分配数量到各行
        new_quantities = self.redistribute_quantities(
            analysis['data_rows'], service_quantity
        )
        
        # 更新工作表数据
        for i, data_row in enumerate(analysis['data_rows']):
            row = data_row['row']
            quantity_cell = f"D{row}"
            
            if i < len(new_quantities):
                modified_sheet['data'][quantity_cell] = new_quantities[i]
            else:
                modified_sheet['data'][quantity_cell] = 0
        
        return modified_sheet
    
    def redistribute_quantities(self, data_rows: List[Dict[str, Any]], 
                              target_quantity: int) -> List[int]:
        """重新分配数量"""
        if not data_rows or target_quantity <= 0:
            return [0] * len(data_rows)
        
        # 按原始比例分配
        original_total = sum(row['quantity'] for row in data_rows)
        if original_total == 0:
            # 平均分配
            base_qty = target_quantity // len(data_rows)
            remainder = target_quantity % len(data_rows)
            
            quantities = [base_qty] * len(data_rows)
            for i in range(remainder):
                quantities[i] += 1
            
            return quantities
        
        # 按比例分配
        quantities = []
        allocated_total = 0
        
        for i, row in enumerate(data_rows):
            if i == len(data_rows) - 1:
                # 最后一行分配剩余的所有数量
                qty = target_quantity - allocated_total
            else:
                # 按比例分配
                ratio = row['quantity'] / original_total
                qty = int(target_quantity * ratio)
                allocated_total += qty
            
            quantities.append(max(0, qty))  # 确保不为负数
        
        return quantities
    
    def get_split_summary(self, workbook_data: Dict[str, Any], 
                         split_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取拆分摘要"""
        summary = {
            'original_file': workbook_data['file_name'],
            'total_sheets': len(workbook_data['sheets']),
            'customer_service_count': len(split_results),
            'split_details': []
        }
        
        for i, result in enumerate(split_results):
            detail = {
                'customer_service_index': i + 1,
                'sheets': list(result['sheets'].keys()),
                'sheet_count': len(result['sheets'])
            }
            summary['split_details'].append(detail)
        
        return summary
