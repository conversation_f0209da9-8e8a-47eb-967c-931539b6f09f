#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例Excel文件用于测试
"""

import openpyxl
from openpyxl.drawing.image import Image
from PIL import Image as PILImage
import os

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 创建工作簿
    wb = openpyxl.Workbook()
    
    # 删除默认工作表
    wb.remove(wb.active)
    
    # 创建示例数据
    sample_data = [
        {
            'sheet_name': '产品组1',
            'data': [
                ['搜索主图', '商品ID', '搜索关键词', '单数', '合计单量', '需求备注'],
                ['图片1', 'P001', '手机壳', 15, 50, '优先处理'],
                ['图片2', 'P002', '充电器', 12, 50, ''],
                ['图片3', 'P003', '数据线', 8, 50, ''],
                ['图片4', 'P004', '耳机', 10, 50, '加急'],
                ['图片5', 'P005', '支架', 5, 50, ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
            ]
        },
        {
            'sheet_name': '产品组2', 
            'data': [
                ['搜索主图', '商品ID', '搜索关键词', '单数', '合计单量', '需求备注'],
                ['图片6', 'P006', '键盘', 20, 60, ''],
                ['图片7', 'P007', '鼠标', 18, 60, ''],
                ['图片8', 'P008', '音响', 12, 60, '重要'],
                ['图片9', 'P009', '摄像头', 10, 60, ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
            ]
        },
        {
            'sheet_name': '产品组3',
            'data': [
                ['搜索主图', '商品ID', '搜索关键词', '单数', '合计单量', '需求备注'],
                ['图片10', 'P010', '显示器', 25, 80, ''],
                ['图片11', 'P011', '打印机', 15, 80, ''],
                ['图片12', 'P012', '扫描仪', 20, 80, ''],
                ['图片13', 'P013', '路由器', 20, 80, '紧急'],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
                ['', '', '', '', '', ''],
            ]
        }
    ]
    
    # 创建工作表并填充数据
    for sheet_info in sample_data:
        ws = wb.create_sheet(title=sheet_info['sheet_name'])
        
        # 填充数据
        for row_idx, row_data in enumerate(sheet_info['data'], 1):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)
        
        # 设置列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 10
        ws.column_dimensions['E'].width = 12
        ws.column_dimensions['F'].width = 15
        
        # 创建示例图片并插入（可选）
        try:
            create_sample_image(f"sample_image_{sheet_info['sheet_name']}.png")
            img = Image(f"sample_image_{sheet_info['sheet_name']}.png")
            img.width = 80
            img.height = 60
            ws.add_image(img, 'A2')
        except Exception as e:
            print(f"添加图片失败: {e}")
    
    # 保存文件
    filename = "示例运营表格.xlsx"
    wb.save(filename)
    print(f"示例Excel文件已创建: {filename}")
    
    return filename

def create_sample_image(filename):
    """创建示例图片"""
    try:
        # 创建一个简单的示例图片
        img = PILImage.new('RGB', (100, 80), color='lightblue')
        img.save(filename)
    except Exception as e:
        print(f"创建示例图片失败: {e}")

def create_multiple_sample_files():
    """创建多个示例文件"""
    files = []
    
    # 创建第一个文件
    file1 = create_sample_excel()
    files.append(file1)
    
    # 创建第二个文件
    wb2 = openpyxl.Workbook()
    wb2.remove(wb2.active)
    
    ws = wb2.create_sheet(title='电子产品')
    data = [
        ['搜索主图', '商品ID', '搜索关键词', '单数', '合计单量', '需求备注'],
        ['图片A', 'E001', '笔记本电脑', 30, 100, '高优先级'],
        ['图片B', 'E002', '平板电脑', 25, 100, ''],
        ['图片C', 'E003', '智能手表', 20, 100, ''],
        ['图片D', 'E004', '蓝牙耳机', 25, 100, '热销'],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
    ]
    
    for row_idx, row_data in enumerate(data, 1):
        for col_idx, value in enumerate(row_data, 1):
            ws.cell(row=row_idx, column=col_idx, value=value)
    
    filename2 = "示例运营表格2.xlsx"
    wb2.save(filename2)
    files.append(filename2)
    print(f"第二个示例Excel文件已创建: {filename2}")
    
    return files

if __name__ == "__main__":
    print("创建示例Excel文件...")
    files = create_multiple_sample_files()
    print(f"已创建 {len(files)} 个示例文件:")
    for file in files:
        print(f"  - {file}")
    print("\n现在可以使用这些文件测试拆分程序了！")
