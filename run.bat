@echo off
chcp 65001 >nul
echo ====================================
echo 运营表格数据自动拆分程序
echo ====================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在检查依赖包...
python -c "import PyQt6, xlwings, PIL" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo 依赖包检查通过
echo.

echo 正在启动程序...
python main.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请查看日志文件
    pause
)
