#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运营表格数据自动拆分程序
主程序入口文件
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from gui_components import MainWindow
from config import APP_CONFIG
from utils import setup_logging


class SplitterApp(QApplication):
    """主应用程序类"""
    
    def __init__(self, argv):
        super().__init__(argv)
        self.setApplicationName(APP_CONFIG['app_name'])
        self.setApplicationVersion(APP_CONFIG['version'])
        self.setOrganizationName(APP_CONFIG['organization'])
        
        # 设置应用程序字体
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
        
        # 设置样式
        self.setStyleSheet(self.get_app_style())
        
        # 创建主窗口
        self.main_window = MainWindow()
        self.main_window.show()
    
    def get_app_style(self):
        """获取应用程序样式"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            text-align: center;
            text-decoration: none;
            font-size: 14px;
            margin: 4px 2px;
            border-radius: 4px;
        }
        
        QPushButton:hover {
            background-color: #45a049;
        }
        
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        QLineEdit {
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            font-size: 14px;
        }
        
        QLineEdit:focus {
            border-color: #4CAF50;
        }
        
        QTextEdit {
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            font-size: 12px;
        }
        
        QProgressBar {
            border: 2px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 3px;
        }
        
        QListWidget {
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            background-color: white;
        }
        
        QListWidget::item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        
        QListWidget::item:selected {
            background-color: #4CAF50;
            color: white;
        }
        """


def main():
    """主函数"""
    # 设置日志系统
    setup_logging()

    # 创建应用程序
    app = SplitterApp(sys.argv)

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
